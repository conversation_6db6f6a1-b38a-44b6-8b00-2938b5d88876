{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1w5xiMTn8NdDL9FJRToAqfzz3+Jz7lcvrTAl/r2eT+s="}}}, "functions": {}, "sortedMiddleware": ["/"]}