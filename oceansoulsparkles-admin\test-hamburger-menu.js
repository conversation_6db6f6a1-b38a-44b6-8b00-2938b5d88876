/**
 * Ocean Soul Sparkles Admin - Hamburger Menu Test Script
 * Run this in the browser console to test hamburger menu functionality
 */

function testHamburgerMenu() {
  console.log('🍔 Testing Hamburger Menu Functionality...');
  
  // Test 1: Check if hamburger button exists
  const hamburgerButton = document.querySelector('[class*="sidebarToggle"]');
  console.log('✅ Hamburger button found:', !!hamburgerButton);
  
  if (!hamburgerButton) {
    console.error('❌ Hamburger button not found!');
    return false;
  }
  
  // Test 2: Check initial state
  const mobileMenu = document.querySelector('[class*="MobileHamburgerMenu"]');
  console.log('✅ Mobile menu component found:', !!mobileMenu);
  
  // Test 3: Simulate click
  console.log('🖱️ Clicking hamburger button...');
  hamburgerButton.click();
  
  // Test 4: Check if menu appears after click
  setTimeout(() => {
    const menuOverlay = document.querySelector('[class*="menuOverlay"]');
    const menuContainer = document.querySelector('[class*="menuContainer"]');
    
    console.log('✅ Menu overlay found:', !!menuOverlay);
    console.log('✅ Menu container found:', !!menuContainer);
    
    if (menuOverlay) {
      const isOpen = menuOverlay.classList.toString().includes('open') || 
                    menuOverlay.style.visibility === 'visible' ||
                    menuOverlay.style.opacity === '1';
      console.log('✅ Menu is open:', isOpen);
      
      if (isOpen) {
        console.log('🎉 SUCCESS: Hamburger menu is working correctly!');
        
        // Test 5: Check menu items
        const menuItems = document.querySelectorAll('[class*="menuItem"]');
        console.log('✅ Menu items found:', menuItems.length);
        
        // Test 6: Test close functionality
        console.log('🖱️ Testing close functionality...');
        setTimeout(() => {
          if (menuOverlay) {
            menuOverlay.click();
            setTimeout(() => {
              const stillOpen = menuOverlay.classList.toString().includes('open');
              console.log('✅ Menu closes on overlay click:', !stillOpen);
              
              if (!stillOpen) {
                console.log('🎉 COMPLETE SUCCESS: All hamburger menu tests passed!');
              }
            }, 500);
          }
        }, 1000);
        
      } else {
        console.error('❌ FAILED: Menu overlay exists but is not visible');
      }
    } else {
      console.error('❌ FAILED: Menu overlay not found after click');
    }
  }, 500);
  
  return true;
}

function checkMobileView() {
  console.log('📱 Checking mobile view...');
  const isMobile = window.innerWidth <= 768;
  console.log('✅ Current width:', window.innerWidth);
  console.log('✅ Is mobile view:', isMobile);
  
  if (!isMobile) {
    console.log('⚠️ Note: Hamburger menu is primarily for mobile view (≤768px)');
    console.log('💡 Try resizing browser window or using device emulation');
  }
  
  return isMobile;
}

function simulateMobileView() {
  console.log('📱 Simulating mobile view...');
  
  // Add mobile viewport meta tag if not present
  let viewport = document.querySelector('meta[name="viewport"]');
  if (!viewport) {
    viewport = document.createElement('meta');
    viewport.name = 'viewport';
    viewport.content = 'width=device-width, initial-scale=1.0';
    document.head.appendChild(viewport);
  }
  
  // Force mobile styles
  document.body.style.width = '375px';
  document.body.style.maxWidth = '375px';
  
  // Trigger resize event
  window.dispatchEvent(new Event('resize'));
  
  console.log('✅ Mobile view simulated (375px width)');
  
  setTimeout(() => {
    testHamburgerMenu();
  }, 1000);
}

function runAllTests() {
  console.log('🧪 Running all hamburger menu tests...');
  console.log('=====================================');
  
  checkMobileView();
  
  setTimeout(() => {
    testHamburgerMenu();
  }, 500);
}

// Auto-run tests if in mobile view
if (window.innerWidth <= 768) {
  console.log('📱 Mobile view detected - running tests automatically...');
  setTimeout(runAllTests, 1000);
} else {
  console.log('💻 Desktop view detected');
  console.log('🔧 To test hamburger menu:');
  console.log('   1. Resize browser to mobile width (≤768px), OR');
  console.log('   2. Run: simulateMobileView()');
  console.log('   3. Run: runAllTests()');
}

// Export functions for manual testing
window.testHamburgerMenu = testHamburgerMenu;
window.checkMobileView = checkMobileView;
window.simulateMobileView = simulateMobileView;
window.runAllTests = runAllTests;

console.log('🍔 Hamburger Menu Test Script Loaded!');
console.log('📋 Available functions:');
console.log('   - testHamburgerMenu()');
console.log('   - checkMobileView()');
console.log('   - simulateMobileView()');
console.log('   - runAllTests()');
